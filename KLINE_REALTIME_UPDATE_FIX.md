# K线实时更新问题修复

## 🐛 问题描述

从BTC切换到其他币种，再切换回BTC后，K线不再实时更新。

## 🔍 根本原因分析

### 问题根源：两套订阅系统冲突

1. **TradingView内部订阅系统** (useDatafeedAction.ts)
   - 通过 `subscribeBars/unsubscribeBars` 管理
   - 使用 `subMap` 存储订阅关系

2. **页面级WebSocket订阅系统** (commonStore.ts)  
   - 通过 `getKlineSocket/cancelKline` 管理
   - 直接与WebSocket服务器通信

### 具体问题

当使用 `setSymbol()` 轻量级切换时：

1. ✅ TradingView调用新的 `subscribeBars` - 创建内部订阅
2. ❌ **页面级WebSocket订阅没有切换** - 仍订阅旧symbol
3. ❌ **实时数据推送错误** - `klineTicker.currentPair` 不匹配
4. ❌ **数据被过滤** - `pair.value !== symbolInfo.fullName` 检查失败

## 🔧 修复方案

### 1. 修复订阅系统同步问题

```typescript
// 在finalizeSymbolSwitch中添加WebSocket订阅切换
const finalizeSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
  // 取消旧symbol的WebSocket订阅
  if (oldSymbol && interval.value) {
    cancelKline(oldSymbol, interval.value)
  }
  
  // 订阅新symbol的WebSocket数据
  if (newSymbol && interval.value) {
    setTimeout(() => {
      getKlineSocket(newSymbol, interval.value)
    }, 100)
  }
}
```

### 2. 修复数据过滤逻辑

```typescript
// 修复前：使用全局pair.value检查，导致数据被错误过滤
if (pair.value !== symbolInfo.fullName || !newPriceData) {
  return
}

// 修复后：使用订阅自身的symbol检查
if (symbolInfo.fullName !== subscription.symbol || !newPriceData) {
  return  
}
```

### 3. 修复watch监听器

```typescript
// 修复前：使用formatSymbol可能导致匹配失败
if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {

// 修复后：直接比较symbol
if (subMap[key] && last && subMap[key].symbol === pair.value) {
```

## ✅ 修复效果

### 修复前的问题流程
1. BTC → ETH：正常切换，ETH实时更新正常
2. ETH → BTC：视觉切换成功，但BTC实时更新失败
3. 原因：BTC的WebSocket订阅被清理，但没有重新建立

### 修复后的正常流程  
1. BTC → ETH：
   - TradingView内部切换到ETH订阅
   - WebSocket取消BTC订阅，建立ETH订阅
   - ETH实时更新正常

2. ETH → BTC：
   - TradingView内部切换到BTC订阅  
   - WebSocket取消ETH订阅，重新建立BTC订阅
   - BTC实时更新恢复正常

## 🧪 测试验证

### 测试步骤
1. 打开专业版K线图
2. 从BTC_USDT切换到ETH_USDT
3. 观察ETH实时数据更新
4. 切换回BTC_USDT  
5. 验证BTC实时数据是否正常更新

### 预期结果
- ✅ 所有币种切换后都能正常实时更新
- ✅ 无loading闪烁，保持流畅体验
- ✅ 数据准确性得到保证

## 📝 技术要点

### 关键修复点
1. **双重订阅同步**：确保TradingView和WebSocket订阅保持一致
2. **数据过滤优化**：使用正确的symbol匹配逻辑
3. **时序控制**：适当的延迟确保订阅切换顺序正确

### 架构改进
- 保持了轻量级切换的流畅体验
- 修复了实时数据推送问题
- 增强了错误处理和容错能力

现在币种切换既流畅又能保证实时数据正确更新！
