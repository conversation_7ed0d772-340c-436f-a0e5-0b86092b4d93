# 深度调试分析 - 币种切换无API调用问题

## 🔍 问题现状

从BTC切换到ETH再切换回BTC后，接口完全没有调用，导致K线图无法显示数据。

## 🧐 深度排查发现的潜在问题

### 1. prepareSymbolSwitch中的提前更新问题

**问题代码**：
```typescript
// 在prepareSymbolSwitch中提前更新pair.value
pair.value = newSymbol  // 这可能导致TradingView认为symbol没有变化
```

**修复**：
```typescript
// 不要提前更新pair.value，让TradingView的机制来处理
console.log(`Current pair.value: ${pair.value}, will switch to: ${newSymbol}`)
```

### 2. 缓存清理可能不彻底

**问题**：clearSymbolCache可能没有完全清除所有相关缓存，导致返回旧数据而不调用API。

**修复**：
- 增强缓存键匹配逻辑
- 添加详细的调试日志
- 确保所有相关缓存都被清除

### 3. TradingView调用流程追踪缺失

**问题**：无法确定TradingView是否正确调用了resolveSymbol和getBars。

**修复**：
- 在resolveSymbol中添加调试日志
- 在getBars中添加详细的参数日志
- 在fetchHistoricalData中添加缓存检查日志

## 🔧 关键修复点

### 1. 修复prepareSymbolSwitch逻辑

```typescript
const prepareSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
  console.log(`Preparing symbol switch from ${oldSymbol} to ${newSymbol}`)
  
  // 1. 清除新symbol的所有缓存
  clearSymbolCache(newSymbol)
  
  // 2. 设置强制刷新标志
  setForceRefresh(true)
  
  // 3. 关键修复：不要提前更新pair.value
  // 让TradingView的机制来处理symbol更新
  console.log(`Current pair.value: ${pair.value}, will switch to: ${newSymbol}`)
  
  // 4. 清理旧订阅
  // ...
}
```

### 2. 增强缓存清理逻辑

```typescript
const clearSymbolCache = (symbol: string) => {
  console.log(`Clearing cache for symbol: ${symbol}`)
  
  // 更严格的缓存键匹配
  const keysToDelete = []
  for (const [key] of dataCache) {
    if (key.startsWith(`${symbol}-`) || key.includes(`${symbol}-`)) {
      keysToDelete.push(key)
    }
  }
  
  console.log(`Found ${keysToDelete.length} cache keys to delete`)
  keysToDelete.forEach(key => {
    dataCache.delete(key)
    console.log(`Deleted cache key: ${key}`)
  })
  
  // 清理其他相关状态...
}
```

### 3. 添加完整的调用流程追踪

```typescript
// resolveSymbol调试
resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
  console.log(`resolveSymbol called for: ${symbolName}, current pair.value: ${pair.value}`)
  // ...
  console.log(`resolveSymbol completed for: ${symbolName}`)
}

// getBars调试
async function getBars(...) {
  console.log(`getBars called: symbol=${currentSymbol}, resolution=${resolution}, firstDataRequest=${firstDataRequest}, countBack=${countBack}`)
  // ...
}

// fetchHistoricalData调试
async function fetchHistoricalData(...) {
  console.log(`fetchHistoricalData: symbol=${symbol}, cacheKey=${cacheKey}, hasCachedData=${!!cachedData}, shouldForceRefresh=${shouldForceRefresh}`)
  
  if (cachedData && !shouldForceRefresh) {
    console.log(`Using cached data for ${symbol}, skipping API call`)
    return
  }
  
  console.log(`Making API call for ${symbol} - cache miss or force refresh`)
  // ...
}
```

## 🧪 调试验证步骤

### 1. 打开浏览器控制台

在测试页面打开开发者工具的控制台，观察日志输出。

### 2. 执行测试流程

1. 初始化：BTC_USDT_SWAP
2. 切换到：ETH_USDT_SWAP
3. **切换回：BTC_USDT_SWAP**

### 3. 观察关键日志

**期望看到的日志序列**：
```
1. performIntelligentSymbolSwitch: BTC_USDT_SWAP -> ETH_USDT_SWAP
2. prepareSymbolSwitch: Current pair.value: BTC_USDT_SWAP, will switch to: ETH_USDT_SWAP
3. clearSymbolCache: Clearing cache for symbol: ETH_USDT_SWAP
4. TradingView setSymbol completed for: ETH_USDT_SWAP
5. resolveSymbol called for: ETH_USDT_SWAP
6. getBars called: symbol=ETH_USDT_SWAP, firstDataRequest=true
7. fetchHistoricalData: Making API call for ETH_USDT_SWAP

// 切换回BTC时
8. performIntelligentSymbolSwitch: ETH_USDT_SWAP -> BTC_USDT_SWAP
9. prepareSymbolSwitch: Current pair.value: ETH_USDT_SWAP, will switch to: BTC_USDT_SWAP
10. clearSymbolCache: Clearing cache for symbol: BTC_USDT_SWAP
11. TradingView setSymbol completed for: BTC_USDT_SWAP
12. resolveSymbol called for: BTC_USDT_SWAP
13. getBars called: symbol=BTC_USDT_SWAP, firstDataRequest=true
14. fetchHistoricalData: Making API call for BTC_USDT_SWAP
```

### 4. 问题诊断

**如果没有看到步骤12-14**：
- TradingView没有调用resolveSymbol/getBars
- 可能是prepareSymbolSwitch中的pair.value提前更新导致的

**如果看到步骤12-13但没有步骤14**：
- getBars被调用了，但fetchHistoricalData使用了缓存
- 检查缓存清理是否彻底

**如果看到"Using cached data"日志**：
- 缓存没有被正确清除
- 需要检查clearSymbolCache的实现

## 📝 下一步行动

1. **测试验证**：按照上述步骤进行测试，观察控制台日志
2. **问题定位**：根据日志输出确定具体的问题环节
3. **针对性修复**：根据问题定位结果进行针对性修复
4. **回归测试**：确保修复后功能正常且没有引入新问题

现在我们有了完整的调试信息，可以精确定位问题所在！
