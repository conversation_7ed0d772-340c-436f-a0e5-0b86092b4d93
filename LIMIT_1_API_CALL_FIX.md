# limit=1异常API调用问题修复

## 🐛 问题描述

从BTC切换到ETH再切换回BTC时，candles接口被调用3次：
1. **limit=300** - 正常的历史数据请求 ✅
2. **limit=21** - 正常的增量数据请求 ✅  
3. **limit=1** - 异常的小数值请求 ❌

## 🔍 根本原因分析

### limit=1调用的来源

在 `fetchHistoricalData` 函数中：
```typescript
const requestLimit = isMonthlyResolution && firstDataRequest ?
  Math.max(countBack > 300 ? 300 : countBack, 50) :
  (countBack > 300 ? 300 : countBack)  // 问题在这里！
```

**问题**：当TradingView传递 `countBack=1` 时，limit就会被设置为1，导致异常的API调用。

### 重复调用的原因

1. **TradingView内部机制**：setSymbol() 可能触发多次 getBars 调用
2. **重复订阅检查缺失**：subscribeBars 没有检查是否已存在相同订阅
3. **WebSocket订阅冲突**：多个地方同时管理WebSocket订阅

## 🔧 修复方案

### 1. 修复limit计算逻辑

```typescript
// 修复前：可能产生limit=1的异常调用
const requestLimit = (countBack > 300 ? 300 : countBack)

// 修复后：确保limit至少为21
let requestLimit
if (isMonthlyResolution && firstDataRequest) {
  requestLimit = Math.max(countBack > 300 ? 300 : countBack, 50)
} else {
  // 确保limit至少为21，避免异常的小数值请求
  requestLimit = Math.max(countBack > 300 ? 300 : countBack, 21)
}
```

### 2. 添加重复订阅检查

```typescript
function subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
  const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
  
  // 检查是否已经存在相同的订阅，避免重复订阅
  if (subMap[subscriptionKey] && subMap[subscriptionKey].symbol === symbolInfo.fullName) {
    console.log(`Subscription already exists for ${subscriptionKey}, skipping duplicate`)
    return
  }
  
  // ... 继续订阅逻辑
}
```

### 3. 移除冲突的WebSocket订阅

```typescript
// 移除subscribeBars中的WebSocket订阅逻辑
// WebSocket订阅应该由页面级别或组件级别统一管理
// 避免与其他订阅系统产生冲突
```

### 4. 增强调试信息

```typescript
// 添加详细的调试日志
console.log(`getBars called: symbol=${currentSymbol}, resolution=${resolution}, firstDataRequest=${firstDataRequest}, countBack=${countBack}`)
console.log(`API call: symbol=${formatSymbol(symbol)}, limit=${requestLimit}, countBack=${countBack}`)
```

## ✅ 修复效果

### 修复前的问题
- **BTC → ETH → BTC**：调用3次API，包括异常的limit=1调用
- **重复订阅**：可能存在多个相同的订阅
- **系统冲突**：多个订阅系统相互干扰

### 修复后的效果
- **BTC → ETH → BTC**：最多调用2次API（limit=300 + limit=21）
- **避免异常调用**：不再出现limit=1的异常请求
- **重复订阅检查**：防止相同订阅的重复创建
- **系统隔离**：减少不同订阅系统间的冲突

## 🧪 测试验证

### 测试步骤
1. 打开专业版K线图，初始化为BTC_USDT_SWAP
2. 切换到ETH_USDT_SWAP
3. 切换回BTC_USDT_SWAP
4. 观察网络请求中的candles调用

### 预期结果
- ✅ 不再出现limit=1的异常调用
- ✅ 最多只有2次正常的API调用
- ✅ 实时数据推送正常工作
- ✅ 保持流畅的切换体验

## 📝 技术要点

### 关键改进
1. **智能limit计算**：确保API调用的合理性
2. **重复订阅防护**：避免不必要的重复订阅
3. **系统解耦**：减少不同订阅系统间的耦合
4. **调试增强**：提供详细的调试信息

### 架构优化
- 遵循单一职责原则
- 减少系统间的相互干扰
- 提高代码的可维护性和稳定性

现在币种切换既高效又稳定，不再有异常的API调用！
