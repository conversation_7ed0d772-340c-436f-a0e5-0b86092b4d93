import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    if (firstDataRequest && resolution === '1M') {
      if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
          klineTicker.value.currentPair === symbolInfo.fullName) {
        preObj.value = klineList.value[0]
        onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
        return true
      }

      const waitForWebSocketData = () => {
        return new Promise((resolve) => {
          let attempts = 0
          const maxAttempts = 20
          const checkInterval = 100

          const checkData = () => {
            attempts++
            if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
                klineTicker.value.currentPair === symbolInfo.fullName) {
              preObj.value = klineList.value[0]
              onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
              resolve(true)
            } else if (attempts >= maxAttempts) {
              resolve(false)
            } else {
              setTimeout(checkData, checkInterval)
            }
          }

          setTimeout(checkData, checkInterval)
        })
      }

      const hasWebSocketData = await waitForWebSocketData()
      return hasWebSocketData
    }
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    console.log(symbolInfo, 'symbolInfoGetBars', periodParams)
    const currentSymbol = symbolInfo.fullName
    const previousSymbol = pair.value

    // 检测symbol变化
    const isSymbolChanged = previousSymbol && previousSymbol !== currentSymbol
    if (isSymbolChanged) {
      console.log(`Symbol changed in getBars: ${previousSymbol} -> ${currentSymbol}`)
      // 强制刷新新symbol的数据
      setForceRefresh(true)
    }

    pair.value = currentSymbol
    interval.value = resolutionMap[resolution]
    key = `${currentSymbol}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    // 调试信息：记录getBars的调用参数
    console.log(`getBars called: symbol=${currentSymbol}, resolution=${resolution}, firstDataRequest=${firstDataRequest}, countBack=${countBack}, from=${from}, to=${to}`)

    if (firstDataRequest && resolution !== '1M') {
      const monthlyKey = `${symbolInfo.fullName}-1M-first`
      if (dataCache.has(monthlyKey)) {
        dataCache.delete(monthlyKey)
      }
      if (klineTicker.value.currentPeriod === '1M') {
        klineList.value = []
        klineTicker.value = {}
      }
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = () => {
    dataCache.clear()
    lastCompleteBar.value = {}
    monthlySubscriptionCache = {
      key: null,
      subscription: null,
      pair: null
    }
    klineList.value = []
    klineTicker.value = {}
    preObj.value = {}

    Object.keys(subMap).forEach(key => {
      delete subMap[key]
    })

    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
  }

  // 新增：清除特定symbol的缓存
  const clearSymbolCache = (symbol: string) => {
    if (!symbol) return

    console.log(`Clearing cache for symbol: ${symbol}`)

    // 清除该symbol相关的数据缓存
    const keysToDelete = []
    for (const [key] of dataCache) {
      if (key.startsWith(`${symbol}-`) || key.includes(`${symbol}-`)) {
        keysToDelete.push(key)
      }
    }
    console.log(`Found ${keysToDelete.length} cache keys to delete for ${symbol}:`, keysToDelete)
    keysToDelete.forEach(key => {
      dataCache.delete(key)
      console.log(`Deleted cache key: ${key}`)
    })

    // 清除该symbol的lastCompleteBar
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key.includes(symbol)) {
        delete lastCompleteBar.value[key]
        console.log(`Deleted lastCompleteBar key: ${key}`)
      }
    })

    // 清除该symbol的订阅
    Object.keys(subMap).forEach(key => {
      if (key.includes(symbol)) {
        delete subMap[key]
        console.log(`Deleted subscription key: ${key}`)
      }
    })

    // 如果是月线缓存的symbol，清除月线缓存
    if (monthlySubscriptionCache.pair === symbol) {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      console.log(`Cleared monthly subscription cache for: ${symbol}`)
    }

    console.log(`Cache clearing completed for symbol: ${symbol}`)
  }

  // 新增：为新symbol准备数据环境
  const prepareSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
    console.log(`Preparing symbol switch from ${oldSymbol} to ${newSymbol}`)

    // 1. 清除新symbol的所有缓存，确保获取最新数据
    clearSymbolCache(newSymbol)

    // 2. 设置强制刷新标志，确保getBars重新获取数据
    setForceRefresh(true)

    // 3. 关键修复：不要提前更新pair.value，让TradingView的机制来处理
    // 提前更新pair.value会导致TradingView认为symbol没有变化，从而不调用getBars
    console.log(`Current pair.value: ${pair.value}, will switch to: ${newSymbol}`)

    // 4. 清除旧symbol的实时订阅（但保留历史数据缓存以备回退）
    Object.keys(subMap).forEach(key => {
      if (key.includes(oldSymbol) && !key.includes(newSymbol)) {
        const subscription = subMap[key]
        if (subscription && typeof subscription === 'object') {
          // 标记为待清理，但不立即删除
          subscription._pendingCleanup = true
        }
      }
    })

    return true
  }

  // 新增：完成symbol切换后的清理工作
  const finalizeSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
    console.log(`Finalizing symbol switch to ${newSymbol}`)

    // 清理标记为待清理的旧订阅
    Object.keys(subMap).forEach(key => {
      const subscription = subMap[key]
      if (subscription && subscription._pendingCleanup) {
        delete subMap[key]
      }
    })

    // 重置强制刷新标志
    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    // 修复：移除手动的WebSocket订阅调用，避免与TradingView的subscribeBars冲突
    // TradingView的setSymbol()会自动触发subscribeBars，我们不应该重复订阅
    // 只需要确保数据状态的正确性即可

    console.log(`Symbol switch to ${newSymbol} completed - letting TradingView handle data subscription`)
  }

  // 新增：更新当前处理的symbol
  const updateSymbol = (newSymbol: string) => {
    pair.value = newSymbol
    console.log(`Updated datafeed symbol to: ${newSymbol}`)
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
    }
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const cacheKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : from}`
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = resolution === '1M'
    const cacheTimeout = isMonthlyResolution ? (firstDataRequest ? 60 * 1000 : 30 * 1000) : CACHE_DURATION

    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    const isFromMonthlyToMinute = hasMonthlyCache && resolution === 1 && firstDataRequest
    const shouldForceRefresh = forceRefresh.value || (hasMonthlyCache && !isMonthlyResolution && firstDataRequest) || isFromMonthlyToMinute

    console.log(`fetchHistoricalData: symbol=${symbol}, cacheKey=${cacheKey}, hasCachedData=${!!cachedData}, shouldForceRefresh=${shouldForceRefresh}, forceRefresh=${forceRefresh.value}`)

    if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout) && firstDataRequest && !shouldForceRefresh) {
      console.log(`Using cached data for ${symbol}, skipping API call`)
      onHistoryCallback(cachedData.data, { noData: cachedData.data.length === 0 })
      return
    }

    console.log(`Making API call for ${symbol} - cache miss or force refresh`)

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    try {
      const now = Date.now()

      // 修复limit计算逻辑，避免异常的小数值请求
      let requestLimit
      if (isMonthlyResolution && firstDataRequest) {
        requestLimit = Math.max(countBack > 300 ? 300 : countBack, 50)
      } else {
        // 确保limit至少为21，避免异常的小数值请求
        requestLimit = Math.max(countBack > 300 ? 300 : countBack, 21)
      }

      // 调试信息：记录API调用参数
      console.log(`API call: symbol=${formatSymbol(symbol)}, limit=${requestLimit}, countBack=${countBack}, firstDataRequest=${firstDataRequest}`)

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: firstDataRequest ? now : preObj.value.time,
        limit: requestLimit,
        origin:1,
      })

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      onErrorCallback(error)
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const resultVal = {
        time: Number(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      monthlySubscription.listen(resultVal)
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    // 修复：确保订阅存在且symbol匹配
    if (subMap[key] && last && subMap[key].symbol === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          interval.value === val2.currentPeriod) {
        resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: Number(val2.time) || Date.now(),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      rafId = requestAnimationFrame(() => {
        if (subMap[key] && subMap[key].listen) {
          subMap[key].listen(resultVal)
        }
        rafId = null
      })
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    console.log(`subscribeBars called for symbol: ${symbolInfo.fullName}, resolution: ${resolution}, subscriberUID: ${subscriberUID}`)

    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    const previousSymbol = pair.value

    // 修复：移除过于严格的重复订阅检查，这会阻止正常的symbol切换
    // TradingView在symbol切换时需要重新创建订阅，即使key相同
    console.log(`Creating subscription for ${subscriptionKey}`)

    // 检测symbol变化
    if (previousSymbol && previousSymbol !== symbolInfo.fullName) {
      console.log(`Symbol changed in subscribeBars: ${previousSymbol} -> ${symbolInfo.fullName}`)

      // 清理旧symbol的WebSocket订阅，避免数据混乱
      const { cancelKline } = commonStore()
      if (previousSymbol && interval.value) {
        console.log(`Cleaning up WebSocket for old symbol: ${previousSymbol}`)
        cancelKline(previousSymbol, interval.value)
      }
    }

    // 更新当前symbol
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]

    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    const currentKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key !== currentKey) {
        delete lastCompleteBar.value[key]
      }
    })

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          // 修复：使用subscription的symbol而不是全局pair.value进行检查
          // 这样可以确保每个订阅只处理自己对应的symbol数据
          if (symbolInfo.fullName !== subscription.symbol || !newPriceData) {
            return
          }
          onRealtimeCallback(newPriceData)
        } catch (error) {
          console.error('Error in realtime callback:', error)
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }

    // 确保WebSocket订阅新symbol的实时数据
    // 只有在symbol真正变化时才重新订阅，避免重复调用
    if (previousSymbol !== symbolInfo.fullName) {
      const { getKlineSocket } = commonStore()
      console.log(`Establishing WebSocket subscription for new symbol: ${symbolInfo.fullName}`)

      // 稍微延迟确保清理完成
      setTimeout(() => {
        getKlineSocket(symbolInfo.fullName, resolutionMap[resolution])
      }, 50)
    }

    console.log(`subscribeBars completed for ${symbolInfo.fullName}`)
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      console.log(`resolveSymbol called for: ${symbolName}, current pair.value: ${pair.value}`)

      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '/') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: ' ', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        console.log(`resolveSymbol completed for: ${symbolName}`)
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    clearSymbolCache,
    prepareSymbolSwitch,
    finalizeSymbolSwitch,
    updateSymbol,
    setForceRefresh
  }
}