<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(333, widget1)
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo, isPairDetail, pair } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  }
})
const resolutionReMap: any = {
  'line': 1,
  '1m': 1,
  '5m': 5,
  '15m': 15,
  '30m': 30,
  '1h': 60,
  '2h': 120,
  '4h': 240,
  '6h': 360,
  '8h': 480,
  '12h': 720,
  '1d': '1d',
  '1w': '1w',
  '1M': '1M'
}
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
const widgetOption = {
  debug: false,
  symbol: props.pair || pair.value,
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: props.resolution ? resolutionReMap[props.resolution] : '15',
  locale: tradingviewLangMap[locale.value] || locale.value,
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null
const Loading = ref(true)
const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }

  if (props.resolution === '1M') {
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  datafeedInstance = useDatafeedAction(pairInfo.value)
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15',
  widget = new window.TradingView.widget(widgetOption)
  console.log(widgetOption, pairInfo.value, 'dhdudheudheuueuhedhdue')
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
  })
}

const previousResolution = ref(props.resolution)

watch(() => props.resolution, (newVal, oldVal) => {
  if (newVal && widget) {
    const isFromMonthly = oldVal === '1M' || previousResolution.value === '1M'
    const isToMonthly = newVal === '1M'
    const isToNonMonthly = newVal !== '1M'

    if ((isFromMonthly && isToNonMonthly) || (!isFromMonthly && isToMonthly)) {
      if (datafeedInstance && datafeedInstance.clearCache) {
        datafeedInstance.clearCache()
      }
      if (datafeedInstance && datafeedInstance.setForceRefresh) {
        datafeedInstance.setForceRefresh(true)
      }

      if (widget) {
        widget.remove()
        widget = null
      }

      setTimeout(() => {
        Loading.value = true
        datafeedInstance = useDatafeedAction(pairInfo.value)
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = props.pair || pair.value
        widgetOption.interval = resolutionReMap[newVal]
        widget = new window.TradingView.widget(widgetOption)

        widget.onChartReady(() => {
          widget.activeChart().setChartType(1)
          widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
          Loading.value = false
        })
      }, 150)
    } else {
      widget.activeChart().setResolution(resolutionReMap[newVal])
    }
  }

  previousResolution.value = oldVal
})
const currentPairSymbol = computed(() => props.pair || pair.value)
const hasChanged = ref(false)

// 备用重建方案 - 当智能切换失败时使用
const fallbackToRebuild = (newVal, oldVal) => {
  console.warn(`Fallback to rebuild method: ${oldVal} -> ${newVal}`)

  try {
    // 清理datafeed状态
    if (datafeedInstance && datafeedInstance.clearCache) {
      datafeedInstance.clearCache()
    }
    if (datafeedInstance && datafeedInstance.setForceRefresh) {
      datafeedInstance.setForceRefresh(true)
    }

    // 移除旧widget
    if (widget) {
      widget.remove()
      widget = null
    }

    // 重建widget
    setTimeout(() => {
      Loading.value = true
      datafeedInstance = useDatafeedAction(pairInfo.value)
      widgetOption.datafeed = datafeedInstance
      widgetOption.symbol = newVal
      widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15'
      widget = new window.TradingView.widget(widgetOption)

      widget.onChartReady(() => {
        widget.activeChart().setChartType(1)
        widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
        Loading.value = false
        console.log(`Fallback rebuild completed for: ${newVal}`)
      })
    }, 200)

  } catch (error) {
    console.error('Fallback rebuild also failed:', error)
    Loading.value = false
    // 这里可以添加用户提示或其他错误处理
  }
}

watch(() => currentPairSymbol.value, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading) {
    performIntelligentSymbolSwitch(newVal, oldVal)
  }
})

// 智能的两阶段symbol切换
const performIntelligentSymbolSwitch = async (newSymbol, oldSymbol) => {
  // 防止重复切换
  if (!newSymbol || !oldSymbol || newSymbol === oldSymbol) {
    console.warn('Invalid symbol switch parameters:', { newSymbol, oldSymbol })
    return
  }

  // 检查widget状态
  if (!widget || !widget.activeChart) {
    console.warn('Widget not ready for symbol switch')
    fallbackToRebuild(newSymbol, oldSymbol)
    return
  }

  try {
    console.log(`Starting intelligent symbol switch: ${oldSymbol} -> ${newSymbol}`)

    // === 第一阶段：数据预准备 ===
    if (datafeedInstance && datafeedInstance.prepareSymbolSwitch) {
      const prepared = datafeedInstance.prepareSymbolSwitch(newSymbol, oldSymbol)
      if (!prepared) {
        throw new Error('Failed to prepare symbol switch')
      }
    } else {
      console.warn('prepareSymbolSwitch method not available, using basic preparation')
      // 基础准备：清除新symbol缓存
      if (datafeedInstance && datafeedInstance.clearSymbolCache) {
        datafeedInstance.clearSymbolCache(newSymbol)
      }
    }

    // === 第二阶段：视觉切换 ===
    // 强制TradingView重新解析symbol，确保触发resolveSymbol和getBars
    console.log(`Forcing chart reset and symbol switch to: ${newSymbol}`)

    // 方法1：先重置数据，再设置symbol
    widget.activeChart().resetData()

    setTimeout(() => {
      widget.activeChart().setSymbol(newSymbol, (symbolInfo) => {
        console.log(`TradingView setSymbol completed for: ${newSymbol}`, symbolInfo)

        // === 第三阶段：后台数据验证和实时订阅 ===
        setTimeout(() => {
          finalizeSymbolSwitchProcess(newSymbol, oldSymbol)
        }, 100) // 给TradingView一点时间完成内部处理
      })
    }, 50) // 给resetData一点时间

    // 不显示Loading，保持流畅体验
    console.log(`Visual switch initiated for: ${newSymbol}`)

  } catch (error) {
    console.error('Intelligent symbol switch failed:', error)
    // 如果任何阶段失败，回退到重建方案
    fallbackToRebuild(newSymbol, oldSymbol)
  }
}

// 完成symbol切换的最终处理
const finalizeSymbolSwitchProcess = (newSymbol, oldSymbol) => {
  try {
    console.log(`Finalizing symbol switch process for: ${newSymbol}`)

    // 1. 完成datafeed的清理工作
    if (datafeedInstance && datafeedInstance.finalizeSymbolSwitch) {
      datafeedInstance.finalizeSymbolSwitch(newSymbol, oldSymbol)
    }

    // 2. 确保WebSocket订阅切换到新symbol
    // 这里可以添加额外的WebSocket重连逻辑

    // 3. 验证数据加载状态
    // 如果需要，可以添加数据验证逻辑

    console.log(`Symbol switch process completed successfully: ${newSymbol}`)

  } catch (error) {
    console.error('Failed to finalize symbol switch:', error)
    // 如果最终处理失败，记录错误但不回退（因为视觉切换已完成）
  }
}

onMounted(() => {
  const currentPair = props.pair || pair.value

  if (currentPair && JSON.stringify(pairInfo.value) !== '{}') {
    initChart()
    hasChanged.value = true
  }
})

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value

  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>