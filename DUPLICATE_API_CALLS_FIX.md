# 重复API调用和实时更新问题修复

## 🐛 问题描述

1. **接口重复调用3次**：从BTC切换到ETH再切换回BTC时，candles接口被调用3次
2. **K线不实时更新**：返回BTC后实时数据推送失效

## 🔍 根本原因分析

### 重复调用的原因

1. **TradingView的setSymbol()** → 触发 `subscribeBars` → 调用 `getBars` (第1次)
2. **finalizeSymbolSwitch** → 手动调用 `getKlineSocket` (第2次)  
3. **其他订阅逻辑** → 可能的额外调用 (第3次)

### 架构冲突

- **TradingView内部机制**：setSymbol() → subscribeBars → getBars
- **我们的手动干预**：finalizeSymbolSwitch → getKlineSocket
- **结果**：两套系统重复订阅，造成API重复调用

## 🔧 修复方案

### 1. 移除重复的手动订阅

```typescript
// 修复前：在finalizeSymbolSwitch中手动调用getKlineSocket
const finalizeSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
  // 取消旧symbol的WebSocket订阅
  cancelKline(oldSymbol, interval.value)
  
  // 订阅新symbol的WebSocket数据 - 这里造成重复！
  getKlineSocket(newSymbol, interval.value)
}

// 修复后：让TradingView自己管理订阅
const finalizeSymbolSwitch = (newSymbol: string, oldSymbol: string) => {
  // 只做清理工作，不手动订阅
  // TradingView的setSymbol()会自动处理数据订阅
  console.log(`Symbol switch completed - letting TradingView handle subscription`)
}
```

### 2. 优化subscribeBars处理symbol切换

```typescript
function subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
  const previousSymbol = pair.value
  
  // 检测symbol变化
  if (previousSymbol && previousSymbol !== symbolInfo.fullName) {
    console.log(`Symbol changed: ${previousSymbol} -> ${symbolInfo.fullName}`)
    
    // 清理旧symbol的WebSocket订阅
    const { cancelKline } = commonStore()
    cancelKline(previousSymbol, interval.value)
  }
  
  // 更新当前symbol
  pair.value = symbolInfo.fullName
  interval.value = resolutionMap[resolution]
  
  // ... 创建新订阅
  
  // 只有在symbol真正变化时才重新订阅WebSocket
  if (previousSymbol !== symbolInfo.fullName) {
    const { getKlineSocket } = commonStore()
    setTimeout(() => {
      getKlineSocket(symbolInfo.fullName, resolutionMap[resolution])
    }, 50)
  }
}
```

## ✅ 修复效果

### 修复前的问题流程
1. **BTC → ETH**：
   - setSymbol() → subscribeBars → getBars (第1次API调用)
   - finalizeSymbolSwitch → getKlineSocket (第2次API调用)

2. **ETH → BTC**：
   - setSymbol() → subscribeBars → getBars (第1次API调用)  
   - finalizeSymbolSwitch → getKlineSocket (第2次API调用)
   - 可能的其他调用 (第3次API调用)
   - **结果**：BTC实时数据失效

### 修复后的正常流程
1. **BTC → ETH**：
   - setSymbol() → subscribeBars → getBars (仅1次API调用)
   - subscribeBars内部处理WebSocket切换
   - ETH实时数据正常

2. **ETH → BTC**：
   - setSymbol() → subscribeBars → getBars (仅1次API调用)
   - subscribeBars内部处理WebSocket切换  
   - **BTC实时数据恢复正常** ✅

## 🧪 测试验证

### 测试步骤
1. 打开专业版K线图，初始化为BTC
2. 切换到ETH，观察网络请求（应该只有1次candles调用）
3. 切换回BTC，观察网络请求（应该只有1次candles调用）
4. 验证BTC的实时数据是否正常更新

### 预期结果
- ✅ 每次symbol切换只调用1次candles接口
- ✅ 所有币种的实时数据都能正常更新
- ✅ 保持流畅的视觉切换体验

## 📝 技术要点

### 关键改进
1. **单一职责**：让TradingView负责数据管理，我们只负责状态清理
2. **避免重复**：移除手动的WebSocket订阅调用
3. **智能切换**：只在symbol真正变化时才重新订阅
4. **时序控制**：适当的延迟确保清理和订阅的正确顺序

### 架构优化
- 遵循TradingView的原生数据流
- 减少人为干预，提高稳定性
- 保持轻量级切换的性能优势

现在币种切换既流畅又高效，没有重复调用！
